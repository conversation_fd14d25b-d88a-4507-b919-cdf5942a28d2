<?php
require_once '../config.php';

header('Content-Type: application/json');

function handleGet()
{
    global $conn;

    try {
        // Check if requesting a specific product
        $id = $_GET['id'] ?? null;

        if ($id) {
            // Get specific product
            $stmt = $conn->prepare(
                "SELECT p.*,
                        CASE
                            WHEN lp.id IS NOT NULL THEN true
                            ELSE false
                        END as has_landing_page,
                        lp.lien_url as landing_url
                 FROM produits p
                 LEFT JOIN landing_pages lp ON p.id = lp.produit_id
                 WHERE p.id = ?"
            );
            $stmt->execute([$id]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$product) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Product not found']);
                return;
            }

            echo json_encode($product);
            return;
        }

        // Get all products
        $stmt = $conn->prepare(
            "SELECT p.*,
                    CASE
                        WHEN lp.id IS NOT NULL THEN true
                        ELSE false
                    END as has_landing_page,
                    lp.lien_url as landing_url
             FROM produits p
             LEFT JOIN landing_pages lp ON p.id = lp.produit_id"
        );
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($products)) {
            echo json_encode(['success' => true, 'products' => []]);
            return;
        }

        // Add full URLs
        foreach ($products as &$product) {
            $product['url'] = '/Mossaab-LandingPage/product.php?id=' . $product['id'];
            if ($product['has_landing_page']) {
                $product['landing_url'] = '/Mossaab-LandingPage/landing-page.php?id=' . $product['id'];
            }
        }

        echo json_encode(['success' => true, 'products' => $products]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function handleToggleActive()
{
    global $conn;

    try {
        // Enhanced error logging
        error_log("Toggle active called with POST data: " . print_r($_POST, true));

        $id = $_POST['productId'] ?? null;
        $active = isset($_POST['active']) ? (bool)$_POST['active'] : null;

        if (!$id || $active === null) {
            error_log("Toggle active validation failed - ID: $id, Active: " . var_export($active, true));
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Product ID and active status are required']);
            return;
        }

        // Convert string values to proper boolean
        if (is_string($_POST['active'])) {
            $active = $_POST['active'] === '1' || $_POST['active'] === 'true';
        }

        error_log("Updating product $id to active status: " . ($active ? 'true' : 'false'));

        $stmt = $conn->prepare("UPDATE produits SET actif = ? WHERE id = ?");
        $result = $stmt->execute([$active ? 1 : 0, $id]);

        if ($result && $stmt->rowCount() > 0) {
            error_log("Product $id status updated successfully");
            echo json_encode(['success' => true, 'message' => 'Product status updated successfully']);
        } else {
            error_log("Product $id not found or no changes made");
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Product not found or no changes made']);
        }
    } catch (PDOException $e) {
        error_log("Database error in toggle active: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    } catch (Exception $e) {
        error_log("General error in toggle active: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
    }
}

function handlePost()
{
    global $conn;

    try {
        $id = $_POST['productId'] ?? null;
        $title = $_POST['productTitle'];
        $price = $_POST['productPrice'];
        $stock = $_POST['productStock'];
        $type = $_POST['productType'];

        // Handle specific fields based on product type
        $specificFields = [];
        switch ($type) {
            case 'book':
                $specificFields['auteur'] = $_POST['bookAuthor'] ?? null;
                break;
            case 'bag':
                $specificFields['materiel'] = $_POST['bagMaterial'] ?? null;
                $specificFields['capacite'] = $_POST['bagCapacity'] ?? null;
                break;
            case 'laptop':
                $specificFields['processeur'] = $_POST['laptopProcessor'] ?? null;
                $specificFields['ram'] = $_POST['laptopRam'] ?? null;
                $specificFields['stockage'] = $_POST['laptopStorage'] ?? null;
                break;
        }

        $conn->beginTransaction();

        // Handle image upload
        $imageUrl = null;
        if (!empty($_FILES['productImage']['tmp_name'])) {
            $uploadDir = '../../uploads/products/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileName = uniqid() . '_' . $_FILES['productImage']['name'];
            $filePath = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['productImage']['tmp_name'], $filePath)) {
                $imageUrl = '/uploads/products/' . $fileName;
            }
        }

        if ($id) {
            // Update existing product
            $sql = "UPDATE produits SET
                    titre = ?,
                    prix = ?,
                    stock = ?,
                    type = ?";
            $params = [$title, $price, $stock, $type];

            if ($imageUrl) {
                $sql .= ", image_url = ?";
                $params[] = $imageUrl;
            }

            foreach ($specificFields as $field => $value) {
                if ($value !== null) {
                    $sql .= ", $field = ?";
                    $params[] = $value;
                }
            }

            $sql .= " WHERE id = ?";
            $params[] = $id;

            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
        } else {
            // Insert new product
            $fields = ['titre', 'prix', 'stock', 'type'];
            $values = [$title, $price, $stock, $type];
            $placeholders = ['?', '?', '?', '?'];

            if ($imageUrl) {
                $fields[] = 'image_url';
                $values[] = $imageUrl;
                $placeholders[] = '?';
            }

            foreach ($specificFields as $field => $value) {
                if ($value !== null) {
                    $fields[] = $field;
                    $values[] = $value;
                    $placeholders[] = '?';
                }
            }

            $sql = "INSERT INTO produits (" . implode(', ', $fields) . ")
                   VALUES (" . implode(', ', $placeholders) . ")";

            $stmt = $conn->prepare($sql);
            $stmt->execute($values);
            $id = $conn->lastInsertId();
        }

        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Product saved successfully', 'id' => $id]);
    } catch (PDOException $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    } catch (Exception $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error']);
    }
}

function handleDelete()
{
    global $conn;

    try {
        $id = $_GET['id'];

        // Get image path before deleting
        $stmt = $conn->prepare("SELECT image_url FROM produits WHERE id = ?");
        $stmt->execute([$id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        // Delete product (cascade will handle landing pages)
        $stmt = $conn->prepare("DELETE FROM produits WHERE id = ?");
        $stmt->execute([$id]);

        // Delete physical image file if exists
        if ($product && $product['image_url']) {
            $filePath = '../../' . ltrim($product['image_url'], '/');
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        echo json_encode(['success' => true, 'message' => 'Product deleted successfully']);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Server error']);
    }
}

// Route requests
// Get request type
$action = $_GET['action'] ?? '';

// Route requests
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
        if ($action === 'toggle-active') {
            handleToggleActive();
        } else {
            handlePost();
        }
        break;
    case 'DELETE':
        handleDelete();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
