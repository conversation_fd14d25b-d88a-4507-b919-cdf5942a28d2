-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1:3307
-- <PERSON><PERSON><PERSON><PERSON> le : mer. 09 juil. 2025 à 11:13
-- Version du serveur : 11.5.2-MariaDB
-- Version de PHP : 8.3.14
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */
;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */
;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */
;
/*!40101 SET NAMES utf8mb4 */
;
--
-- Base de données : `mossab-landing-page`
--

-- --------------------------------------------------------
--
-- Structure de la table `admins`
--

DROP TABLE IF EXISTS `admins`;
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom_utilisateur` varchar(50) NOT NULL,
  `mot_de_passe` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom_utilisateur` (`nom_utilisateur`)
) ENGINE = InnoDB AUTO_INCREMENT = 3 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
--
-- Déchargement des données de la table `admins`
--

INSERT INTO `admins` (`id`, `nom_utilisateur`, `mot_de_passe`)
VALUES (
    1,
    'admin',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
  );
-- --------------------------------------------------------
--
-- Structure de la table `commandes`
--

DROP TABLE IF EXISTS `commandes`;
CREATE TABLE IF NOT EXISTS `commandes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(255) DEFAULT NULL,
  `nom_client` varchar(255) DEFAULT NULL,
  `telephone` varchar(50) DEFAULT NULL,
  `adresse_livraison` text DEFAULT NULL,
  `montant_total` decimal(10, 2) DEFAULT NULL,
  `nombre_articles` int(11) DEFAULT 0,
  `statut` enum('en_attente', 'payé', 'expédié') DEFAULT 'en_attente',
  `numero_suivi` varchar(50) DEFAULT NULL,
  `notes_admin` text DEFAULT NULL,
  `date_commande` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_date_commande` (`date_commande`),
  KEY `idx_statut` (`statut`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `commande_items`
--

DROP TABLE IF EXISTS `commande_items`;
CREATE TABLE IF NOT EXISTS `commande_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commande_id` int(11) NOT NULL,
  `produit_id` int(11) NOT NULL,
  `quantite` int(11) NOT NULL,
  `prix_unitaire` decimal(10, 2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `commande_id` (`commande_id`),
  KEY `produit_id` (`produit_id`)
) ENGINE = MyISAM DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;
-- --------------------------------------------------------
--
-- Structure de la table `details_commande`
--

DROP TABLE IF EXISTS `details_commande`;
CREATE TABLE IF NOT EXISTS `details_commande` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commande_id` int(11) DEFAULT NULL,
  `livre_id` int(11) DEFAULT NULL,
  `quantite` int(11) DEFAULT NULL,
  `prix_unitaire` decimal(10, 2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `commande_id` (`commande_id`),
  KEY `livre_id` (`livre_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `landing_pages`
--

DROP TABLE IF EXISTS `landing_pages`;
CREATE TABLE IF NOT EXISTS `landing_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `produit_id` int(11) NOT NULL,
  `titre` varchar(255) NOT NULL,
  `contenu_droit` text DEFAULT NULL,
  `contenu_gauche` text DEFAULT NULL,
  `lien_url` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_landing_pages_produit` (`produit_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 8 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `landing_page_images`
--

DROP TABLE IF EXISTS `landing_page_images`;
CREATE TABLE IF NOT EXISTS `landing_page_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `landing_page_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `ordre` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_landing_page_images_ordre` (`landing_page_id`, `ordre`)
) ENGINE = InnoDB AUTO_INCREMENT = 11 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL,
  `message` text NOT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `panier`
--

DROP TABLE IF EXISTS `panier`;
CREATE TABLE IF NOT EXISTS `panier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(255) DEFAULT NULL,
  `livre_id` int(11) DEFAULT NULL,
  `quantite` int(11) DEFAULT 1,
  `date_ajout` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `livre_id` (`livre_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `product_content_blocks`
--

DROP TABLE IF EXISTS `product_content_blocks`;
CREATE TABLE IF NOT EXISTS `product_content_blocks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `content` text NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `product_images`
--

DROP TABLE IF EXISTS `product_images`;
CREATE TABLE IF NOT EXISTS `product_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_images_ibfk_1` (`product_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `produits`
--

DROP TABLE IF EXISTS `produits`;
CREATE TABLE IF NOT EXISTS `produits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('book', 'bag', 'laptop') NOT NULL,
  `titre` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `prix` decimal(10, 2) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT 0,
  `image_url` varchar(255) DEFAULT NULL,
  `actif` tinyint(1) DEFAULT 1,
  `auteur` varchar(255) DEFAULT NULL,
  `materiel` varchar(100) DEFAULT NULL,
  `capacite` varchar(50) DEFAULT NULL,
  `processeur` varchar(100) DEFAULT NULL,
  `ram` varchar(50) DEFAULT NULL,
  `stockage` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `has_landing_page` tinyint(1) DEFAULT 0,
  `landing_page_enabled` tinyint(1) DEFAULT 0,
  `slug` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_produits_type` (`type`),
  KEY `idx_actif` (`actif`)
) ENGINE = InnoDB AUTO_INCREMENT = 10 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
--
-- Déchargement des données de la table `produits`
--

INSERT INTO `produits` (
    `id`,
    `type`,
    `titre`,
    `description`,
    `prix`,
    `stock`,
    `image_url`,
    `actif`,
    `auteur`,
    `materiel`,
    `capacite`,
    `processeur`,
    `ram`,
    `stockage`,
    `created_at`,
    `updated_at`,
    `has_landing_page`,
    `landing_page_enabled`,
    `slug`
  )
VALUES (
    1,
    'book',
    'كتاب البرمجة للمبتدئين',
    NULL,
    299.99,
    25,
    '/images/default-book.jpg',
    1,
    'محمد أحمد',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    '2025-07-08 09:55:17',
    '2025-07-08 09:55:17',
    0,
    0,
    NULL
  ),
  (
    3,
    'bag',
    'حقيبة ظهر للحاسوب',
    NULL,
    199.99,
    50,
    '/images/default-bag.jpg',
    1,
    NULL,
    'نايلون مقاوم للماء',
    '30L',
    NULL,
    NULL,
    NULL,
    '2025-07-08 09:55:17',
    '2025-07-08 09:55:17',
    0,
    0,
    NULL
  ),
  (
    4,
    'book',
    'العادات الذرية',
    'كتاب يشرح كيفية بناء عادات جيدة والتخلص من العادات السيئة',
    2500.00,
    50,
    'images/book1.svg',
    1,
    'جيمس كلير',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    '2025-07-08 10:27:01',
    '2025-07-08 10:27:01',
    0,
    0,
    NULL
  ),
  (
    5,
    'book',
    'قوة العادات',
    'لماذا نفعل ما نفعل في الحياة والأعمال',
    2200.00,
    45,
    'images/book2.svg',
    1,
    'تشارلز دوهيج',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    '2025-07-08 10:27:01',
    '2025-07-08 10:27:01',
    0,
    0,
    NULL
  ),
  (
    6,
    'book',
    'فن اللامبالاة',
    'نهج معاكس للعيش حياة جيدة',
    1800.00,
    30,
    'images/book3.svg',
    1,
    'مارك مانسون',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    '2025-07-08 10:27:01',
    '2025-07-08 10:27:01',
    0,
    0,
    NULL
  );
-- --------------------------------------------------------
--
-- Structure de la table `recus_paiement`
--

DROP TABLE IF EXISTS `recus_paiement`;
CREATE TABLE IF NOT EXISTS `recus_paiement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `commande_id` int(11) DEFAULT NULL,
  `numero_recu` varchar(100) DEFAULT NULL,
  `image_recu_url` varchar(255) DEFAULT NULL,
  `date_reception` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `commande_id` (`commande_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- --------------------------------------------------------
--
-- Structure de la table `store_settings`
--

DROP TABLE IF EXISTS `store_settings`;
CREATE TABLE IF NOT EXISTS `store_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE = MyISAM AUTO_INCREMENT = 8 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;
--
-- Déchargement des données de la table `store_settings`
--

INSERT INTO `store_settings` (
    `id`,
    `setting_key`,
    `setting_value`,
    `created_at`,
    `updated_at`
  )
VALUES (
    1,
    'store_name',
    'Mossaab Store',
    '2025-07-07 22:53:25',
    '2025-07-07 22:53:25'
  ),
  (
    2,
    'store_description',
    'Your one-stop shop for books and electronics',
    '2025-07-07 22:53:25',
    '2025-07-07 22:53:25'
  ),
  (
    3,
    'contact_email',
    '<EMAIL>',
    '2025-07-07 22:53:25',
    '2025-07-07 22:53:25'
  ),
  (
    4,
    'phone_number',
    '+213 000000000',
    '2025-07-07 22:53:25',
    '2025-07-07 22:53:25'
  ),
  (
    5,
    'address',
    'Algeria',
    '2025-07-07 22:53:25',
    '2025-07-07 22:53:25'
  ),
  (
    6,
    'shipping_policy',
    'Standard shipping within 3-5 business days',
    '2025-07-07 22:53:25',
    '2025-07-07 22:53:25'
  ),
  (
    7,
    'return_policy',
    '30-day return policy for unused items',
    '2025-07-07 22:53:25',
    '2025-07-07 22:53:25'
  );
-- --------------------------------------------------------
--
-- Structure de la table `utilisateurs`
--

DROP TABLE IF EXISTS `utilisateurs`;
CREATE TABLE IF NOT EXISTS `utilisateurs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) DEFAULT NULL,
  `email` varchar(150) DEFAULT NULL,
  `mot_de_passe` varchar(255) DEFAULT NULL,
  `date_creation` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `details_commande`
--
ALTER TABLE `details_commande`
ADD CONSTRAINT `details_commande_ibfk_1` FOREIGN KEY (`commande_id`) REFERENCES `commandes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `details_commande_ibfk_2` FOREIGN KEY (`livre_id`) REFERENCES `produits` (`id`);
--
-- Contraintes pour la table `landing_pages`
--
ALTER TABLE `landing_pages`
ADD CONSTRAINT `landing_pages_ibfk_1` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE;
--
-- Contraintes pour la table `landing_page_images`
--
ALTER TABLE `landing_page_images`
ADD CONSTRAINT `landing_page_images_ibfk_1` FOREIGN KEY (`landing_page_id`) REFERENCES `landing_pages` (`id`) ON DELETE CASCADE;
--
-- Contraintes pour la table `panier`
--
ALTER TABLE `panier`
ADD CONSTRAINT `panier_ibfk_1` FOREIGN KEY (`livre_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE;
--
-- Contraintes pour la table `product_content_blocks`
--
ALTER TABLE `product_content_blocks`
ADD CONSTRAINT `product_content_blocks_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE;
--
-- Contraintes pour la table `product_images`
--
ALTER TABLE `product_images`
ADD CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE;
--
-- Contraintes pour la table `recus_paiement`
--
ALTER TABLE `recus_paiement`
ADD CONSTRAINT `recus_paiement_ibfk_1` FOREIGN KEY (`commande_id`) REFERENCES `commandes` (`id`);
COMMIT;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */
;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */
;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */
;
