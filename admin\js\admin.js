// Gestionnaire de notifications
const notificationManager = {
    showSuccess(message) {
        const notification = {
            id: Date.now(),
            type: 'success',
            message: message
        };
        this.showNotification(notification);
    },

    showError(message) {
        const notification = {
            id: Date.now(),
            type: 'error',
            message: message
        };
        this.showNotification(notification);
    },

    showInfo(message) {
        const notification = {
            id: Date.now(),
            type: 'info',
            message: message
        };
        this.showNotification(notification);
    },
    container: null,
    checkInterval: null,
    unreadCount: 0,

    init() {
        this.container = document.getElementById('notificationsContainer');
        this.startPolling();
    },

    startPolling() {
        this.checkNotifications();
        this.checkInterval = setInterval(() => this.checkNotifications(), 30000); // Vérifier toutes les 30 secondes
    },

    async checkNotifications() {
        try {
            const response = await fetch('../php/notifications.php?action=unread');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const text = await response.text();
            if (!text.trim()) {
                console.warn('Empty response from notifications API');
                return;
            }

            const notifications = JSON.parse(text);

            if (Array.isArray(notifications)) {
                // Mettre à jour le compteur
                this.unreadCount = notifications.length;
                this.updateBadge();

                // Afficher les nouvelles notifications
                notifications.forEach(notification => this.showNotification(notification));
            }
        } catch (error) {
            console.error('Erreur lors de la vérification des notifications:', error);
        }
    },

    showNotification(notification) {
        const existingNotif = document.querySelector(`[data-notification-id="${notification.id}"]`);
        if (existingNotif) return;

        const notifElement = document.createElement('div');
        notifElement.className = `notification ${notification.type} unread`;
        notifElement.dataset.notificationId = notification.id;

        notifElement.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${this.getNotificationTitle(notification.type)}</div>
                <div class="notification-message">${notification.message}</div>
            </div>
            <button class="notification-close" onclick="notificationManager.dismissNotification(${notification.id})">
                &times;
            </button>
        `;

        this.container.appendChild(notifElement);

        // Auto-dismiss success and info notifications after 5 seconds
        if (notification.type === 'success' || notification.type === 'info') {
            setTimeout(() => {
                this.dismissNotification(notification.id);
            }, 5000);
        }

        // Jouer un son de notification (with better error handling)
        try {
            const audio = new Audio('../assets/notification.mp3');
            audio.volume = 0.3; // Lower volume
            audio.preload = 'none'; // Don't preload to avoid metadata errors

            // Only try to play if user has interacted with the page
            if (document.hasFocus()) {
                const playPromise = audio.play();
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        // Silently fail - notification sound is not critical
                        console.debug('Notification sound not available:', error.message);
                    });
                }
            }
        } catch (error) {
            // Silently fail - notification sound is not critical
            console.debug('Notification sound initialization failed:', error.message);
        }
    },

    getNotificationTitle(type) {
        const titles = {
            'new_order': 'طلب جديد',
            'payment_received': 'تم استلام الدفع',
            'low_stock': 'تنبيه المخزون'
        };
        return titles[type] || 'إشعار';
    },

    updateBadge() {
        const ordersLink = document.querySelector('[data-section="orders"]');
        let badge = ordersLink.querySelector('.notification-badge');

        if (this.unreadCount > 0) {
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'notification-badge';
                ordersLink.appendChild(badge);
            }
            badge.textContent = this.unreadCount;
        } else if (badge) {
            badge.remove();
        }
    },

    // Dismiss notification immediately (for local notifications)
    dismissNotification(id) {
        const notif = document.querySelector(`[data-notification-id="${id}"]`);
        if (notif) {
            notif.style.opacity = '0';
            notif.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notif.remove();
            }, 300);
        }
    },

    // Close notification and mark as read (for server notifications)
    async closeNotification(id) {
        try {
            await fetch(`../php/notifications.php?action=mark_read&id=${id}`);
            const notif = document.querySelector(`[data-notification-id="${id}"]`);
            if (notif) {
                notif.style.opacity = '0';
                notif.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notif.remove();
                }, 300);
                this.unreadCount = Math.max(0, this.unreadCount - 1);
                this.updateBadge();
            }
        } catch (error) {
            console.error('Erreur lors de la fermeture de la notification:', error);
            // Still remove the notification from UI even if server request fails
            this.dismissNotification(id);
        }
    },

    // Clear all notifications
    clearAllNotifications() {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notif => {
            const id = notif.dataset.notificationId;
            this.dismissNotification(id);
        });
    }
};

// Vérifier l'authentification
function checkAuth() {
    fetch('../php/admin.php?action=check')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            if (!text.trim()) {
                console.warn('Empty response from auth check');
                window.location.href = 'login.html';
                return;
            }
            const data = JSON.parse(text);
            if (!data.logged_in) {
                window.location.href = 'login.html';
            }
        })
        .catch(error => {
            console.error('Auth check error:', error);
            window.location.href = 'login.html';
        });
}

// Load settings data
async function loadSettings() {
    try {
        console.log('Loading settings...');
        const response = await fetch('../php/admin.php?action=get_store_settings');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseText = await response.text();
        console.log('Settings response text:', responseText);

        if (!responseText.trim()) {
            console.warn('Empty response from settings API');
            return;
        }

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Response was:', responseText.substring(0, 200));
            throw new Error('Invalid JSON response from settings API');
        }

        console.log('Parsed settings data:', data);

        // Handle different response formats
        let settings = {};
        if (data.success && data.settings) {
            // Format: {success: true, settings: {key: value}}
            settings = data.settings;
        } else if (data.settings) {
            // Format: {settings: {key: value}}
            settings = data.settings;
        } else {
            // Direct format: {key: value}
            settings = data;
        }

        console.log('Final settings object:', settings);

        // Fill form fields
        const storeNameField = document.getElementById('storeName');
        const storePhoneField = document.getElementById('storePhone');
        const storeEmailField = document.getElementById('storeEmail');
        const storeAddressField = document.getElementById('storeAddress');

        if (storeNameField) storeNameField.value = settings.store_name || '';
        if (storePhoneField) storePhoneField.value = settings.phone_number || settings.store_phone || '';
        if (storeEmailField) storeEmailField.value = settings.contact_email || settings.store_email || '';
        if (storeAddressField) storeAddressField.value = settings.address || settings.store_address || '';

        // Update TinyMCE for storeAddress field
        if (window.tinymce && storeAddressField) {
            const editor = tinymce.get('storeAddress');
            if (editor) {
                editor.setContent(settings.address || settings.store_address || '');
            }
        }

        console.log('Settings loaded successfully');
    } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        // Don't show alert to user for settings, just log it
    }
}

// Handle store settings form submission
document.getElementById('storeSettingsForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    try {
        const formData = new FormData();
        formData.append('store_name', document.getElementById('storeName').value);
        formData.append('store_phone', document.getElementById('storePhone').value);
        formData.append('store_email', document.getElementById('storeEmail').value);
        formData.append('store_address', tinymce.get('storeAddress')?.getContent() || document.getElementById('storeAddress').value);

        const response = await fetch('../php/admin.php?action=update_store_settings', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            alert('تم تحديث إعدادات المتجر بنجاح');
        } else {
            throw new Error(result.error || 'خطأ غير معروف');
        }
    } catch (error) {
        console.error('Erreur lors de la mise à jour des paramètres:', error);
        alert('Une erreur est survenue lors de la mise à jour des paramètres du magasin');
    }
});

// Fix for context menu selection error
document.addEventListener('selectionchange', function() {
    if (window.getSelection && !window.getSelection().rangeCount) {
        const selection = window.getSelection();
        const range = document.createRange();
        range.setStart(document.body, 0);
        range.setEnd(document.body, 0);
        selection.addRange(range);
    }
});

// Add View More link to products table
function addViewMoreLink(row, product) {
    if (product.has_landing_page && product.landing_page_enabled) {
        const actionsCell = row.querySelector('td:last-child');
        const viewMoreLink = document.createElement('a');
        viewMoreLink.href = `/product-landing.php?slug=${product.slug}`;
        viewMoreLink.className = 'action-button view-more';
        viewMoreLink.target = '_blank';
        viewMoreLink.innerHTML = '<i class="fas fa-external-link-alt"></i> عرض الصفحة';
        actionsCell.appendChild(viewMoreLink);

        const shareContainer = document.createElement('div');
        shareContainer.className = 'share-buttons';

        // Facebook Share Button
        const fbShareBtn = document.createElement('button');
        fbShareBtn.className = 'share-button facebook';
        fbShareBtn.innerHTML = '<i class="fab fa-facebook-f"></i> فيسبوك';
        fbShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const fbUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(fbUrl, '_blank', 'width=600,height=400');
        };

        // Twitter Share Button
        const twitterShareBtn = document.createElement('button');
        twitterShareBtn.className = 'share-button twitter';
        twitterShareBtn.innerHTML = '<i class="fab fa-twitter"></i> تويتر';
        twitterShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;
            window.open(twitterUrl, '_blank', 'width=600,height=400');
        };

        // WhatsApp Share Button
        const whatsappShareBtn = document.createElement('button');
        whatsappShareBtn.className = 'share-button whatsapp';
        whatsappShareBtn.innerHTML = '<i class="fab fa-whatsapp"></i> واتساب';
        whatsappShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(url)}`;
            window.open(whatsappUrl, '_blank');
        };

        // Copy URL Button
        const copyUrlBtn = document.createElement('button');
        copyUrlBtn.className = 'share-button copy';
        copyUrlBtn.innerHTML = '<i class="fas fa-copy"></i> نسخ الرابط';
        copyUrlBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            navigator.clipboard.writeText(url).then(() => {
                alert('تم نسخ الرابط بنجاح');

                // Change button text temporarily
                const originalText = copyUrlBtn.innerHTML;
                copyUrlBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                setTimeout(() => {
                    copyUrlBtn.innerHTML = originalText;
                }, 2000);
            });
        };

        shareContainer.appendChild(fbShareBtn);
        shareContainer.appendChild(twitterShareBtn);
        shareContainer.appendChild(whatsappShareBtn);
        shareContainer.appendChild(copyUrlBtn);
        actionsCell.appendChild(shareContainer);
    }
}

// Navigation dans le panneau d'administration
function initNavigation() {
    console.log('Initializing navigation...');

    // Find navigation items
    const navItems = document.querySelectorAll('.admin-nav ul li');
    console.log('Found navigation items:', navItems.length);

    if (navItems.length === 0) {
        console.error('No navigation items found!');
        return;
    }

    navItems.forEach((item, index) => {
        console.log(`Adding click listener to item ${index}:`, item);

        item.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Navigation item clicked:', this);

            // Handle logout button
            if (this.id === 'logoutBtn') {
                console.log('Logout button clicked');
                if (typeof logout === 'function') {
                    logout();
                }
                return;
            }

            const sectionId = this.getAttribute('data-section');
            if (!sectionId) return;

            console.log('Switching to section:', sectionId);

            // Remove active class from all nav items and sections
            document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                navItem.classList.remove('active');
            });
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Add active class to clicked nav item and corresponding section
            this.classList.add('active');
            const section = document.getElementById(sectionId);

            // Update page title
            updatePageTitle(sectionId);

            if (section) {
                section.classList.add('active');

                // Load section specific content
                switch(sectionId) {
                    case 'dashboard':
                        console.log('Loading dashboard...');
                        if (typeof loadDashboard === 'function') loadDashboard();
                        break;
                    case 'orders':
                        console.log('Loading orders...');
                        if (typeof loadOrders === 'function') loadOrders();
                        break;
                    case 'books':
                        console.log('Loading books...');
                        if (typeof loadBooks === 'function') {
                            console.log('📦 Calling loadBooks function...');
                            loadBooks();
                        } else {
                            console.error('❌ loadBooks function not found');
                        }
                        break;
                    case 'landingPages':
                        console.log('Loading landing pages...');
                        // Landing pages manager is auto-initialized, just ensure it's ready
                        if (typeof landingPagesManager !== 'undefined') {
                            console.log('Landing Pages Manager is available');
                            // Don't re-initialize, just ensure it's ready
                            if (!landingPagesManager.initialized) {
                                console.log('Landing Pages Manager not initialized yet, initializing...');
                                landingPagesManager.init();
                            } else {
                                console.log('Landing Pages Manager already initialized, refreshing data...');
                                // Always refresh the landing pages data when switching to this section
                                landingPagesManager.loadLandingPages();
                            }
                        } else {
                            console.warn('Landing Pages Manager not found');
                        }
                        break;
                    case 'settings':
                        console.log('Loading settings...');
                        if (typeof loadSettings === 'function') {
                            loadSettings();
                            // Réinitialiser TinyMCE pour le champ storeAddress
                            if (window.tinymce) {
                                tinymce.remove('#storeAddress');
                                window.initTinyMCE();
                            }
                        }
                        break;
                }

                // Close mobile menu if open
                closeMobileMenu();
            } else {
                console.error('Section not found:', sectionId);
            }
        });
    });

    console.log('Navigation initialization complete');
}

// Mobile menu functionality
function initMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.getElementById('sidebar');

    if (mobileToggle && sidebar) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');

            // Update toggle icon
            const icon = this.querySelector('i');
            if (sidebar.classList.contains('mobile-open')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                closeMobileMenu();
            }
        });
    }
}

function closeMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const mobileToggle = document.getElementById('mobileMenuToggle');

    if (sidebar && sidebar.classList.contains('mobile-open')) {
        sidebar.classList.remove('mobile-open');

        if (mobileToggle) {
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-bars';
            }
        }
    }
}

// Add filters for orders section
function addOrdersFilters() {
    const ordersSection = document.getElementById('orders');
    if (ordersSection && !ordersSection.querySelector('.orders-filters')) {
        const filtersHtml = `
            <div class="orders-filters">
                <div class="filter-group">
                    <label for="statusFilter">الحالة:</label>
                    <select id="statusFilter">
                        <option value="">الكل</option>
                        <option value="en_attente">قيد الانتظار</option>
                        <option value="payé">تم الدفع</option>
                        <option value="expédié">تم الشحن</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dateFilter">التاريخ:</label>
                    <select id="dateFilter">
                        <option value="">كل التواريخ</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>
                </div>
            </div>
        `;
        ordersSection.insertAdjacentHTML('afterbegin', filtersHtml);

        // Add event listeners for filters
        const filters = ordersSection.querySelectorAll('select');
        filters.forEach(filter => {
            filter.addEventListener('change', () => {
                const activeFilters = {
                    status: document.getElementById('statusFilter').value,
                    date: document.getElementById('dateFilter').value
                };
                loadOrders(activeFilters);
            });
        });
    }
}

// Load dashboard data
async function loadDashboard() {
    try {
        const response = await fetch('../php/api/dashboard-stats.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Erreur lors du chargement des statistiques');
        }

        const data = result.data;
        console.log('Dashboard data loaded:', data);

        // Update dashboard stats with real data
        const totalBooksElement = document.getElementById('totalBooks');
        const newOrdersElement = document.getElementById('newOrders');
        const totalSalesElement = document.getElementById('totalSales');

        if (totalBooksElement) {
            totalBooksElement.textContent = data.products.total || 0;
        }
        if (newOrdersElement) {
            newOrdersElement.textContent = data.orders.pending || 0;
        }
        if (totalSalesElement) {
            totalSalesElement.textContent = data.sales.formatted_total || '0.00 دج';
        }

        // Update recent orders table if it exists
        const recentOrdersBody = document.querySelector('#recentOrdersTable tbody');
        if (recentOrdersBody && data.recent_orders) {
            recentOrdersBody.innerHTML = '';

            data.recent_orders.forEach(order => {
                const tr = document.createElement('tr');
                tr.style.cursor = 'pointer';
                tr.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${order.nom_client || 'غير محدد'}</td>
                    <td>${order.montant_total || 0} دج</td>
                    <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                    <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                `;
                tr.onclick = () => {
                    document.querySelector('[data-section="orders"]').click();
                    setTimeout(() => showOrderDetails(order.id), 100);
                };
                recentOrdersBody.appendChild(tr);
            });
        }

        console.log('✅ Dashboard statistics updated successfully');
    } catch (error) {
        console.error('❌ Error loading dashboard stats:', error);
        // Set default values instead of showing error notification
        const totalBooksEl = document.getElementById('totalBooks');
        const newOrdersEl = document.getElementById('newOrders');
        const totalSalesEl = document.getElementById('totalSales');

        if (totalBooksEl) totalBooksEl.textContent = '0';
        if (newOrdersEl) newOrdersEl.textContent = '0';
        if (totalSalesEl) totalSalesEl.textContent = '0 دج';

        // Clear recent orders table
        const recentOrdersBody = document.querySelector('#recentOrdersTable tbody');
        if (recentOrdersBody) {
            recentOrdersBody.innerHTML = '<tr><td colspan="5">لا توجد بيانات متاحة</td></tr>';
        }
    }
}

// Charger la liste des produits
async function loadBooks() {
    try {
        console.log('📦 Loading products...');
        const response = await fetch('../php/api/products.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        console.log('API Response:', result);

        // Handle both array and object responses
        let data = [];
        if (Array.isArray(result)) {
            data = result;
        } else if (result.success && Array.isArray(result.products)) {
            data = result.products;
        } else if (result.products) {
            data = result.products;
        } else {
            throw new Error('Invalid data format: no products found');
        }

        // Log data for debugging
        console.log('Products data:', data);
        console.log(`Found ${data.length} products`);

        const tbody = document.querySelector('#booksTable tbody');
        tbody.innerHTML = '';

        data.forEach(product => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td><img src="../${product.image_url}" alt="${product.titre}" width="50"></td>
                    <td>${product.titre}</td>
                    <td>${getProductTypeText(product.type)}</td>
                    <td>${product.prix} دج</td>
                    <td>${product.stock}</td>
                    <td>
                        <span class="status-badge status-${product.actif ? 'active' : 'inactive'}">
                            ${product.actif ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <button onclick="editBook(${product.id})" class="action-button" title="تعديل المنتج">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleProductStatus(${product.id}, ${product.actif})"
                                class="action-button toggle-status-btn"
                                data-product-id="${product.id}"
                                data-current-status="${product.actif}"
                                style="background: ${product.actif ? '#e67e22' : '#27ae60'};"
                                title="${product.actif ? 'تعطيل المنتج' : 'تفعيل المنتج'}">
                            <i class="fas ${product.actif ? 'fa-pause' : 'fa-play'}"></i>
                            <span class="status-text">${product.actif ? 'مفعل' : 'معطل'}</span>
                        </button>
                        <button onclick="deleteBook(${product.id})" class="action-button" style="background: #e74c3c;" title="حذف المنتج">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);

                // Add View More link if product has landing page
                addViewMoreLink(tr, product);
            });
        } catch (error) {
            console.error('Error loading books:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات');
        }
}

// Get product type text in Arabic
function getProductTypeText(type) {
    const types = {
        'book': 'كتاب',
        'backpack': 'حقيبة ظهر',
        'laptop': 'حاسوب محمول'
    };
    return types[type] || type;
}

// Enhanced function to toggle product status
async function toggleProductStatus(productId, currentStatus) {
    console.log('Toggle product status called:', { productId, currentStatus });

    const button = document.querySelector(`[data-product-id="${productId}"]`);
    const originalButtonContent = button ? button.innerHTML : '';

    // Show loading state
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
    }

    try {
        const formData = new FormData();
        formData.append('productId', productId.toString());
        formData.append('active', !currentStatus ? '1' : '0');

        console.log('Sending request with data:', {
            productId: productId.toString(),
            active: !currentStatus ? '1' : '0'
        });

        const response = await fetch('../php/api/products.php?action=toggle-active', {
            method: 'POST',
            body: formData
        });

        console.log('Response status:', response.status);

        // Get response text first to handle both JSON and non-JSON responses
        const responseText = await response.text();
        console.log('Response text:', responseText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}, response: ${responseText}`);
        }

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            throw new Error(`Invalid JSON response: ${responseText}`);
        }

        console.log('Parsed response data:', data);

        if (data.success) {
            // Update button immediately for better UX
            const newStatus = !currentStatus;
            if (button) {
                button.style.background = newStatus ? '#e67e22' : '#27ae60';
                button.title = newStatus ? 'تعطيل المنتج' : 'تفعيل المنتج';
                button.innerHTML = `<i class="fas ${newStatus ? 'fa-pause' : 'fa-play'}"></i> <span class="status-text">${newStatus ? 'مفعل' : 'معطل'}</span>`;
                button.setAttribute('data-current-status', newStatus);
                button.onclick = () => toggleProductStatus(productId, newStatus);
                button.disabled = false;
            }

            // Show success notification
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess(`تم ${newStatus ? 'تفعيل' : 'تعطيل'} المنتج بنجاح`);
            }

            // Refresh the product selection in landing page modal if it's open
            if (typeof landingPagesManager !== 'undefined' && landingPagesManager.refreshProductSelect) {
                landingPagesManager.refreshProductSelect();
            }

        } else {
            throw new Error(data.message || data.error || 'فشل في تحديث حالة المنتج');
        }

    } catch (error) {
        console.error('Error toggling product status:', error);

        // Reset button state
        if (button) {
            button.disabled = false;
            button.innerHTML = originalButtonContent;
            button.style.background = currentStatus ? '#e67e22' : '#27ae60';
            button.innerHTML = `<i class="fas ${currentStatus ? 'fa-pause' : 'fa-play'}"></i> <span class="status-text">${currentStatus ? 'مفعل' : 'معطل'}</span>`;
        }

        notificationManager.showError(error.message || 'حدث خطأ أثناء تحديث حالة المنتج');
    }
}

// Charger la liste des commandes
async function loadOrders(filters = {}) {
    try {
        let url = '../php/orders.php';
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams(filters);
            url += '?' + params.toString();
        }

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();
        if (!text.trim()) {
            console.warn('Empty response from orders API');
            return;
        }

        const orders = JSON.parse(text);
        const ordersTableBody = document.querySelector('#ordersTable tbody');
        ordersTableBody.innerHTML = '';

        orders.forEach(order => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>#${order.id}</td>
                <td>
                    <div class="customer-info">
                        <strong>${order.nom_client}</strong>
                        <span class="customer-email">${order.email || ''}</span>
                    </div>
                </td>
                <td>
                    <div class="order-details">
                        <strong>${order.montant_total} دج</strong>
                        <span class="items-count">${order.items ? order.items.length : 0} منتجات</span>
                    </div>
                </td>
                <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                <td>
                    <button onclick="showOrderDetails(${order.id})" class="action-button"><i class="fas fa-eye"></i></button>
                    <button onclick="printOrder(${order.id})" class="action-button"><i class="fas fa-print"></i></button>
                    <select onchange="updateOrderStatus(${order.id}, this.value)">
                        <option value="en_attente" ${order.statut === 'en_attente' ? 'selected' : ''}>قيد الانتظار</option>
                        <option value="payé" ${order.statut === 'payé' ? 'selected' : ''}>تم الدفع</option>
                        <option value="expédié" ${order.statut === 'expédié' ? 'selected' : ''}>تم الشحن</option>
                    </select>
                </td>
            `;
            ordersTableBody.appendChild(tr);
        });
    } catch (error) {
        console.error('Error loading orders:', error);
    }
}

// Initialize TinyMCE
function initTinyMCE() {
    // Add tinymce class to target textareas
    document.querySelectorAll('#productDescription, .block-content').forEach(el => {
        el.classList.add('tinymce');
    });

    // Use configuration from tinymce-config.js
    if (typeof tinymce !== 'undefined' && typeof tinymce.init === 'function') {
        tinymce.init({
            selector: 'textarea.tinymce',
            language: 'ar',
            language_url: '/admin/js/langs/ar.js',
            directionality: 'rtl',
            content_css: [
                'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap',
                '/admin/css/tinymce-content.css'
            ],
            font_family_formats: 'Noto Sans Arabic=Noto Sans Arabic,sans-serif',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                'preview', 'anchor', 'searchreplace', 'visualblocks', 'code',
                'fullscreen', 'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | preview fullscreen',
            promotion: false,
            branding: false,
            min_height: 400,
            resize: true,
            setup: function(editor) {
                editor.on('init', function() {
                    editor.getContainer().style.direction = 'rtl';
                });
            }
        });
    }
}

// Global error handler for selection issues
window.addEventListener('error', function(e) {
    if (e.message && e.message.includes('rangeCount') || e.message.includes('selection is null')) {
        console.warn('DOM selection error handled globally:', e.message);
        e.preventDefault();
        return true;
    }
});



// Initialize form handlers
function initFormHandlers() {
    // Handle product type selection
    const productTypeSelect = document.getElementById('productType');
    if (productTypeSelect) {
        productTypeSelect.addEventListener('change', (e) => {
            const productType = e.target.value;
            document.querySelectorAll('.field-group').forEach(group => {
                group.style.display = 'none';
                group.classList.remove('active');
            });
            const targetField = document.getElementById(`${productType}Fields`);
            if (targetField) {
                targetField.style.display = 'block';
                targetField.classList.add('active');
            }
        });
    }

    // Handle product form submission
    const bookForm = document.getElementById('bookForm');
    if (bookForm) {
        bookForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const productType = document.getElementById('productType').value;

            // Common fields
            formData.append('type', productType);
            formData.append('titre', document.getElementById('productTitle').value);
            formData.append('description', document.getElementById('productDescription').value);
            formData.append('prix', document.getElementById('productPrice').value);
            formData.append('stock', document.getElementById('productStock').value);

            // Type-specific fields
            switch(productType) {
                case 'book':
                    formData.append('auteur', document.getElementById('bookAuthor').value);
                    break;
                case 'backpack':
                    formData.append('material', document.getElementById('backpackMaterial').value);
                    formData.append('capacity', document.getElementById('backpackCapacity').value);
                    break;
                case 'laptop':
                    formData.append('processor', document.getElementById('laptopProcessor').value);
                    formData.append('ram', document.getElementById('laptopRam').value);
                    formData.append('storage', document.getElementById('laptopStorage').value);
                    break;
            }

            const imageFile = document.getElementById('productImage').files[0];
            if (imageFile) {
                formData.append('image', imageFile);
            }

            // Landing page data (if productLandingManager exists)
            if (typeof productLandingManager !== 'undefined') {
                const landingData = productLandingManager.getFormData();
                formData.append('has_landing_page', landingData.hasLandingPage);
                formData.append('landing_page_enabled', landingData.landingPageEnabled);

                // Append content blocks
                landingData.contentBlocks.forEach((block, index) => {
                    formData.append(`content_blocks[${index}][title]`, block.title);
                    formData.append(`content_blocks[${index}][content]`, block.content);
                    formData.append(`content_blocks[${index}][sort_order]`, block.sortOrder);
                });

                // Append gallery images
                landingData.galleryImages.forEach((file, index) => {
                    formData.append(`gallery_images[${index}]`, file);
                });
            }

            const bookId = e.target.getAttribute('data-book-id');
            const method = bookId ? 'PUT' : 'POST';
            const url = '../php/books.php' + (bookId ? `?id=${bookId}` : '');

            fetch(url, {
                method: method,
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(text => {
                if (!text.trim()) {
                    throw new Error('Empty response from server');
                }
                const data = JSON.parse(text);
                if (data.success) {
                    closeModal('bookModal');
                    loadBooks();
                } else {
                    alert(data.error || 'حدث خطأ أثناء حفظ المنتج');
                }
            })
            .catch(error => {
                console.error('Error saving product:', error);
                alert('حدث خطأ أثناء حفظ المنتج');
            });
        });
    }
}

// Fonctions utilitaires
function getStatusText(status) {
    const statusMap = {
        'en_attente': 'قيد الانتظار',
        'payé': 'تم الدفع',
        'expédié': 'تم الشحن'
    };
    return statusMap[status] || status;
}

function showModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function logout() {
    fetch('../php/admin.php?action=logout', {
        method: 'POST'
    })
    .then(() => {
        window.location.href = 'login.html';
    });
}

// Initialize modal handlers
function initModalHandlers() {
    // Gestionnaires d'événements pour les modals
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });

    const addBookBtn = document.getElementById('addBookBtn');
    if (addBookBtn) {
        addBookBtn.addEventListener('click', function() {
            const bookForm = document.getElementById('bookForm');
            const modalTitle = document.getElementById('modalTitle');
            if (bookForm) bookForm.reset();
            if (bookForm) bookForm.removeAttribute('data-book-id');
            if (modalTitle) modalTitle.textContent = 'إضافة كتاب جديد';
            showModal('bookModal');
        });
    }
}

// Initialize settings form handlers
function initSettingsHandlers() {
    // Handle change password form
    const changePasswordForm = document.getElementById('changePasswordForm');
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword !== confirmPassword) {
                alert('كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين');
                return;
            }

            if (newPassword.length < 6) {
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            try {
                const response = await fetch('../php/admin.php?action=change_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        current_password: currentPassword,
                        new_password: newPassword
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                if (!text.trim()) {
                    throw new Error('Empty response from server');
                }

                const data = JSON.parse(text);
                if (data.success) {
                    alert('تم تغيير كلمة المرور بنجاح');
                    changePasswordForm.reset();
                } else {
                    alert(data.error || 'حدث خطأ أثناء تغيير كلمة المرور');
                }
            } catch (error) {
                console.error('Error changing password:', error);
                alert('حدث خطأ أثناء تغيير كلمة المرور');
            }
        });
    }

    // Handle store settings form
    const storeSettingsForm = document.getElementById('storeSettingsForm');
    if (storeSettingsForm) {
        storeSettingsForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = {
                store_name: document.getElementById('storeName').value,
                store_phone: document.getElementById('storePhone').value,
                store_email: document.getElementById('storeEmail').value,
                store_address: document.getElementById('storeAddress').value
            };

            try {
                const response = await fetch('../php/admin.php?action=update_store_settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const text = await response.text();
                if (!text.trim()) {
                    throw new Error('Empty response from server');
                }

                const data = JSON.parse(text);
                if (data.success) {
                    alert('تم حفظ إعدادات المتجر بنجاح');
                } else {
                    alert(data.error || 'حدث خطأ أثناء حفظ إعدادات المتجر');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ إعدادات المتجر');
            }
        });
    }
}

// Add View More link for products with landing pages
function addViewMoreLink(tr, product) {
    console.log('Adding view more link for product:', {
        id: product.id,
        has_landing_page: product.has_landing_page,
        landing_url: product.landing_url
    });

    if (product.has_landing_page) {
        const td = tr.querySelector('td:last-child');
        const viewMoreLink = document.createElement('a');
        viewMoreLink.href = product.landing_url;
        viewMoreLink.target = '_blank';
        viewMoreLink.className = 'view-more-link';
        viewMoreLink.innerHTML = '<i class="fas fa-external-link-alt"></i>';
        td.appendChild(viewMoreLink);
    }
}

// Load store settings on page load
async function loadStoreSettings() {
    try {
        const response = await fetch('../php/api/store-settings.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();
        if (!text.trim()) {
            console.warn('Empty response from store settings API');
            return;
        }

        let data;
        try {
            data = JSON.parse(text);
        } catch (parseError) {
            console.warn('Invalid JSON response from store settings API:', text.substring(0, 100));
            return;
        }

        if (!data || typeof data !== 'object') {
            throw new Error('Invalid response format');
        }

        if (data.success && data.data) {
            console.log('Store settings loaded successfully:', data.data);
        }

        if (data.success && data.settings) {
            // Map API field names to form field names
            const storeNameEl = document.getElementById('storeName');
            const storePhoneEl = document.getElementById('storePhone');
            const storeEmailEl = document.getElementById('storeEmail');
            const storeAddressEl = document.getElementById('storeAddress');

            if (storeNameEl) storeNameEl.value = data.settings.store_name || '';
            if (storePhoneEl) storePhoneEl.value = data.settings.phone_number || '';
            if (storeEmailEl) storeEmailEl.value = data.settings.contact_email || '';

            // Handle address field (might be TinyMCE)
            if (window.tinymce && tinymce.get('storeAddress')) {
                tinymce.get('storeAddress').setContent(data.settings.address || '');
            } else if (storeAddressEl) {
                storeAddressEl.value = data.settings.address || '';
            }

            console.log('Store settings loaded successfully');
        } else {
            console.warn('Store settings response format:', data);
            throw new Error(data.error || 'Failed to load store settings');
        }
    } catch (error) {
        console.error('Error loading store settings:', error);
        // Set default values instead of showing error notification
        const storeNameEl = document.getElementById('storeName');
        const storePhoneEl = document.getElementById('storePhone');
        const storeEmailEl = document.getElementById('storeEmail');
        const storeAddressEl = document.getElementById('storeAddress');

        if (storeNameEl) storeNameEl.value = '';
        if (storePhoneEl) storePhoneEl.value = '';
        if (storeEmailEl) storeEmailEl.value = '';
        if (storeAddressEl) storeAddressEl.value = '';
    }
}



// Missing function implementations
async function editBook(productId) {
    console.log('🖊️ Starting edit for product ID:', productId);

    try {
        // Show loading notification
        notificationManager.showInfo('جاري تحميل بيانات المنتج...');

        const response = await fetch(`../php/api/products.php?id=${productId}`);
        console.log('📡 Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();
        console.log('📄 Raw response:', text.substring(0, 200) + '...');

        if (!text.trim()) {
            throw new Error('Empty response from server');
        }

        let product;
        try {
            product = JSON.parse(text);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        if (!product || typeof product !== 'object') {
            throw new Error('Invalid response format');
        }

        console.log('✅ Product data loaded:', product);

        // Clear form first
        resetProductForm();

        // Populate form with product data
        const titleField = document.getElementById('productTitle');
        const descField = document.getElementById('productDescription');
        const priceField = document.getElementById('productPrice');
        const stockField = document.getElementById('productStock');
        const typeField = document.getElementById('productType');

        if (titleField) titleField.value = product.titre || '';
        if (descField) descField.value = product.description || '';
        if (priceField) priceField.value = product.prix || '';
        if (stockField) stockField.value = product.stock || '';
        if (typeField) typeField.value = product.type || 'book';

        console.log('📝 Basic fields populated');

        // Handle author field for books
        const authorField = document.getElementById('bookAuthor');
        if (authorField) {
            authorField.value = product.type === 'book' ? (product.auteur || '') : '';
            const authorGroup = authorField.closest('.form-group');
            if (authorGroup) {
                authorGroup.style.display = product.type === 'book' ? 'block' : 'none';
            }
        }

        // Handle product type specific fields
        handleProductTypeChange(product.type);

        // Handle existing image
        if (product.image_url) {
            displayExistingProductImage(product.image_url);
        }

        // Set form to edit mode
        const form = document.getElementById('bookForm');
        if (form) {
            form.setAttribute('data-product-id', productId);
        }

        const modalTitle = document.getElementById('modalTitle');
        if (modalTitle) {
            modalTitle.textContent = 'تعديل المنتج';
        }

        console.log('✅ Form populated successfully');
        notificationManager.showSuccess('تم تحميل بيانات المنتج بنجاح');

        showModal('bookModal');

    } catch (error) {
        console.error('❌ Error loading product:', error);
        notificationManager.showError('حدث خطأ أثناء تحميل بيانات المنتج: ' + error.message);
    }
}

// Helper function to reset the product form
function resetProductForm() {
    const form = document.getElementById('bookForm');
    if (form) {
        form.reset();
        form.removeAttribute('data-product-id');
    }

    // Clear image preview
    const imagePreview = document.getElementById('imagePreview');
    if (imagePreview) {
        imagePreview.innerHTML = '';
    }

    // Reset modal title
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = 'إضافة منتج جديد';
    }
}

// Helper function to display existing product image
function displayExistingProductImage(imageUrl) {
    const imagePreview = document.getElementById('imagePreview');
    if (!imagePreview || !imageUrl) return;

    imagePreview.innerHTML = `
        <div class="preview-image existing-image" style="background-image: url(${imageUrl})">
            <button type="button" class="remove-image" onclick="removeExistingImage(this)">
                <i class="fas fa-times"></i>
            </button>
            <input type="hidden" name="existing_image" value="${imageUrl}">
        </div>
    `;
}

// Helper function to remove existing image
function removeExistingImage(button) {
    const preview = button.closest('.preview-image');
    if (preview) {
        preview.remove();
    }
}

// Helper function to handle product type changes
function handleProductTypeChange(productType) {
    // Hide all type-specific fields first
    document.querySelectorAll('.field-group').forEach(group => {
        group.style.display = 'none';
        group.classList.remove('active');
    });

    // Show relevant fields for the selected type
    const targetField = document.getElementById(`${productType}Fields`);
    if (targetField) {
        targetField.style.display = 'block';
        targetField.classList.add('active');
    }
}

async function deleteBook(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        try {
            const response = await fetch(`../php/api/products.php?id=${productId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                notificationManager.showSuccess('تم حذف المنتج بنجاح');
                loadBooks();
            } else {
                throw new Error(result.error || 'Failed to delete product');
            }
        } catch (error) {
            console.error('Error deleting product:', error);
            notificationManager.showError('حدث خطأ أثناء حذف المنتج');
        }
    }
}

function showOrderDetails(orderId) {
    fetch(`../php/orders.php?id=${orderId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            if (!text.trim()) {
                throw new Error('Empty response from server');
            }
            const order = JSON.parse(text);
            if (order) {
                const orderDetails = document.getElementById('orderDetails');
                orderDetails.innerHTML = `
                    <h4>تفاصيل الطلب #${order.id}</h4>
                    <p><strong>العميل:</strong> ${order.nom_client}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${order.email || 'غير محدد'}</p>
                    <p><strong>الهاتف:</strong> ${order.telephone || 'غير محدد'}</p>
                    <p><strong>العنوان:</strong> ${order.adresse || 'غير محدد'}</p>
                    <p><strong>المبلغ الإجمالي:</strong> ${order.montant_total} دج</p>
                    <p><strong>الحالة:</strong> ${getStatusText(order.statut)}</p>
                    <p><strong>تاريخ الطلب:</strong> ${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</p>
                `;
                showModal('orderModal');
            }
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            alert('حدث خطأ أثناء تحميل تفاصيل الطلب');
        });
}

function printOrder(orderId) {
    window.open(`../php/print-order.php?id=${orderId}`, '_blank');
}

function updateOrderStatus(orderId, newStatus) {
    fetch(`../php/orders.php?id=${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(text => {
        if (!text.trim()) {
            throw new Error('Empty response from server');
        }
        const result = JSON.parse(text);
        if (result.success) {
            loadOrders();
            alert('تم تحديث حالة الطلب بنجاح');
        } else {
            alert(result.error || 'حدث خطأ أثناء تحديث حالة الطلب');
        }
    })
    .catch(error => {
        console.error('Error updating order status:', error);
        alert('حدث خطأ أثناء تحديث حالة الطلب');
    });
}

// Page refresh functionality
function refreshCurrentPage() {
    const refreshBtn = document.getElementById('refreshPageBtn');
    if (!refreshBtn) return;

    // Add loading state
    refreshBtn.classList.add('loading');
    refreshBtn.disabled = true;

    // Get current active section
    const activeSection = document.querySelector('.content-section.active');
    const currentSection = activeSection ? activeSection.id : 'dashboard';

    // Update page title
    updatePageTitle(currentSection);

    // Refresh data based on current section
    const refreshPromises = [];

    switch (currentSection) {
        case 'dashboard':
            refreshPromises.push(loadDashboard());
            break;
        case 'books':
            refreshPromises.push(loadBooks());
            break;
        case 'orders':
            refreshPromises.push(loadOrders());
            break;
        case 'landingPages':
            if (typeof landingPagesManager !== 'undefined' && landingPagesManager.loadLandingPages) {
                refreshPromises.push(landingPagesManager.loadLandingPages());
                // Also refresh the product selection dropdown
                if (landingPagesManager.loadActiveProducts) {
                    refreshPromises.push(landingPagesManager.loadActiveProducts());
                }
            }
            break;
        default:
            refreshPromises.push(loadDashboard());
    }

    // Wait for all refresh operations to complete
    Promise.all(refreshPromises)
        .then(() => {
            notificationManager.showSuccess('تم تحديث الصفحة بنجاح');
        })
        .catch((error) => {
            console.error('Error refreshing page:', error);
            notificationManager.showError('حدث خطأ أثناء تحديث الصفحة');
        })
        .finally(() => {
            // Remove loading state
            setTimeout(() => {
                refreshBtn.classList.remove('loading');
                refreshBtn.disabled = false;
            }, 500);
        });
}

// Update page title based on current section
function updatePageTitle(sectionId) {
    const pageTitle = document.getElementById('pageTitle');
    const titles = {
        'dashboard': 'لوحة المعلومات',
        'books': 'إدارة المنتجات',
        'orders': 'إدارة الطلبات',
        'landingPages': 'صفحات الهبوط',
        'settings': 'الإعدادات'
    };

    if (pageTitle) {
        pageTitle.textContent = titles[sectionId] || 'لوحة التحكم';
    }
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Starting initialization...');
    checkAuth();
    notificationManager.init();
    initNavigation();
    initMobileMenu();
    initFormHandlers();
    initModalHandlers();
    initSettingsHandlers();
    initTinyMCE();
    loadDashboard();
    loadStoreSettings();

    // Initialize refresh button
    const refreshBtn = document.getElementById('refreshPageBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshCurrentPage);
    }

    // landingPagesManager will be initialized in its own DOMContentLoaded event
    console.log('Initialization complete');
});

// Test function for landing page modal
function testLandingPageModal() {
    console.log('🧪 Testing landing page modal...');

    // Check if landingPagesManager exists
    if (typeof landingPagesManager !== 'undefined') {
        console.log('✅ landingPagesManager found');
        console.log('Initialized:', landingPagesManager.initialized);

        if (landingPagesManager.initialized) {
            console.log('🚀 Opening modal via landingPagesManager...');
            landingPagesManager.openModal();
        } else {
            console.log('⚠️ landingPagesManager not initialized, trying to initialize...');
            landingPagesManager.init();
            setTimeout(() => {
                landingPagesManager.openModal();
            }, 500);
        }
    } else {
        console.error('❌ landingPagesManager not found');

        // Try direct modal manipulation
        const modal = document.getElementById('landingPageModal');
        if (modal) {
            console.log('🔧 Trying direct modal manipulation...');
            modal.style.display = 'block';
            modal.style.opacity = '1';
            modal.style.visibility = 'visible';
            modal.style.zIndex = '9999';
        } else {
            console.error('❌ Modal element not found');
        }
    }
}
