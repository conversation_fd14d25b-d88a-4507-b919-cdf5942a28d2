<?php
require_once '../config.php';

header('Content-Type: application/json');

function generateUniqueUrl($productId)
{
    return '/landing/product-' . $productId . '-' . uniqid();
}

function handleGet()
{
    global $conn;

    if (!$conn) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database connection error']);
        return;
    }

    try {
        // Check if requesting a specific landing page
        $id = $_GET['id'] ?? null;

        if ($id) {
            // Get specific landing page
            $stmt = $conn->prepare(
                "SELECT lp.*, p.titre as product_title
                 FROM landing_pages lp
                 JOIN produits p ON lp.produit_id = p.id
                 WHERE lp.id = ?"
            );
            $stmt->execute([$id]);
            $page = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$page) {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Landing page not found']);
                return;
            }

            // Get images for this landing page
            $stmt = $conn->prepare(
                "SELECT image_url
                 FROM landing_page_images
                 WHERE landing_page_id = ?
                 ORDER BY ordre"
            );
            $stmt->execute([$id]);
            $page['images'] = $stmt->fetchAll(PDO::FETCH_COLUMN);

            echo json_encode(['success' => true, 'data' => $page]);
        } else {
            // Get all landing pages
            $stmt = $conn->prepare(
                "SELECT lp.*, p.titre as product_title
                 FROM landing_pages lp
                 JOIN produits p ON lp.produit_id = p.id"
            );
            $stmt->execute();
            $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get images for each landing page
            foreach ($pages as &$page) {
                $stmt = $conn->prepare(
                    "SELECT image_url
                     FROM landing_page_images
                     WHERE landing_page_id = ?
                     ORDER BY ordre"
                );
                $stmt->execute([$page['id']]);
                $page['images'] = $stmt->fetchAll(PDO::FETCH_COLUMN);
            }

            echo json_encode(['success' => true, 'data' => $pages]);
        }
    } catch (PDOException $e) {
        error_log('Database error in handleGet: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred',
            'error_code' => $e->getCode()
        ]);
    }
}

function handlePost()
{
    global $conn;

    if (!$conn) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database connection error']);
        return;
    }

    if (!$conn->inTransaction()) {
        $conn->beginTransaction();
    }

    try {
        // Debug: Log all POST data
        error_log("POST data received: " . print_r($_POST, true));
        error_log("FILES data received: " . print_r($_FILES, true));

        // Get form data with proper field names
        $productId = isset($_POST['productSelect']) ? $_POST['productSelect'] : null;
        $title = isset($_POST['landingPageTitle']) ? $_POST['landingPageTitle'] : null;
        $rightContent = isset($_POST['rightContent']) ? $_POST['rightContent'] : '';
        $leftContent = isset($_POST['leftContent']) ? $_POST['leftContent'] : '';

        // Validate required fields
        if (!$productId) {
            throw new Exception('Product ID is required');
        }
        if (!$title) {
            throw new Exception('Landing page title is required');
        }

        $linkUrl = generateUniqueUrl($productId);

        $conn->beginTransaction();

        // Insert landing page
        $templateId = isset($_POST['template_id']) ? $_POST['template_id'] : 'custom';
        $stmt = $conn->prepare(
            "INSERT INTO landing_pages
             (produit_id, titre, contenu_droit, contenu_gauche, lien_url, template_id)
             VALUES (?, ?, ?, ?, ?, ?)"
        );
        $stmt->execute([$productId, $title, $rightContent, $leftContent, $linkUrl, $templateId]);
        $landingPageId = $conn->lastInsertId();

        // Handle image uploads
        $imageOrder = 0;
        if (!empty($_FILES['landingPageImages'])) {
            $uploadDir = '../../uploads/products/landing/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Handle both single file and multiple files
            $tmpNames = is_array($_FILES['landingPageImages']['tmp_name'])
                ? $_FILES['landingPageImages']['tmp_name']
                : [$_FILES['landingPageImages']['tmp_name']];

            $fileNames = is_array($_FILES['landingPageImages']['name'])
                ? $_FILES['landingPageImages']['name']
                : [$_FILES['landingPageImages']['name']];

            foreach ($tmpNames as $key => $tmpName) {
                if (!empty($tmpName) && is_uploaded_file($tmpName)) {
                    $originalName = $fileNames[$key];
                    $fileExtension = pathinfo($originalName, PATHINFO_EXTENSION);
                    $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
                    $filePath = $uploadDir . $fileName;

                    if (move_uploaded_file($tmpName, $filePath)) {
                        $stmt = $conn->prepare(
                            "INSERT INTO landing_page_images
                             (landing_page_id, image_url, ordre)
                             VALUES (?, ?, ?)"
                        );
                        $stmt->execute([$landingPageId, '/uploads/products/landing/' . $fileName, $imageOrder]);
                        $imageOrder++;
                        error_log("Successfully uploaded file: " . $fileName);
                    } else {
                        error_log("Failed to upload file: " . $originalName);
                    }
                }
            }
        }

        // Handle URL images
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'imageUrls') === 0 && !empty($value)) {
                // Handle both single values and arrays
                if (is_array($value)) {
                    foreach ($value as $url) {
                        if (!empty(trim($url))) {
                            $stmt = $conn->prepare(
                                "INSERT INTO landing_page_images (landing_page_id, image_url, ordre) VALUES (?, ?, ?)"
                            );
                            $stmt->execute([$landingPageId, trim($url), $imageOrder]);
                            $imageOrder++;
                        }
                    }
                } else {
                    if (!empty(trim($value))) {
                        $stmt = $conn->prepare(
                            "INSERT INTO landing_page_images (landing_page_id, image_url, ordre) VALUES (?, ?, ?)"
                        );
                        $stmt->execute([$landingPageId, trim($value), $imageOrder]);
                        $imageOrder++;
                    }
                }
            }
        }

        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Landing page created successfully']);
    } catch (PDOException $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Landing page creation database error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred',
            'error_code' => $e->getCode()
        ]);
    } catch (Exception $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Landing page creation error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error occurred',
            'error_code' => 500
        ]);
    }
}

function handleDelete()
{
    global $conn;

    if (!$conn) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database connection error']);
        return;
    }

    if (!$conn->inTransaction()) {
        $conn->beginTransaction();
    }

    try {
        $id = $_GET['id'];

        // Get image paths before deleting
        $stmt = $conn->prepare("SELECT image_url FROM landing_page_images WHERE landing_page_id = ?");
        $stmt->execute([$id]);
        $images = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Delete landing page (cascade will handle images in DB)
        $stmt = $conn->prepare("DELETE FROM landing_pages WHERE id = ?");
        $stmt->execute([$id]);

        // Delete physical image files
        foreach ($images as $image) {
            $filePath = '../../' . ltrim($image, '/');
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        echo json_encode(['success' => true, 'message' => 'Landing page deleted successfully']);
    } catch (PDOException $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Database error in handleDelete: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred',
            'error_code' => $e->getCode()
        ]);
    } catch (Exception $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Error in handleDelete: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error occurred',
            'error_code' => 500
        ]);
    }
}

function handleUpdate()
{
    global $conn;

    if (!$conn) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database connection error']);
        return;
    }

    if (!$conn->inTransaction()) {
        $conn->beginTransaction();
    }

    try {
        // Get form data with proper field names
        $id = isset($_POST['id']) ? $_POST['id'] : null;
        $productId = isset($_POST['productSelect']) ? $_POST['productSelect'] : null;
        $title = isset($_POST['landingPageTitle']) ? $_POST['landingPageTitle'] : null;
        $rightContent = isset($_POST['rightContent']) ? $_POST['rightContent'] : '';
        $leftContent = isset($_POST['leftContent']) ? $_POST['leftContent'] : '';

        // Validate required fields
        if (!$id) {
            throw new Exception('Landing page ID is required');
        }
        if (!$productId) {
            throw new Exception('Product ID is required');
        }
        if (!$title) {
            throw new Exception('Landing page title is required');
        }

        $conn->beginTransaction();

        $templateId = isset($_POST['template_id']) ? $_POST['template_id'] : 'custom';
        // Update landing page
        $stmt = $conn->prepare(
            "UPDATE landing_pages
             SET produit_id = ?, titre = ?, contenu_droit = ?, contenu_gauche = ?, template_id = ?
             WHERE id = ?"
        );
        $stmt->execute([$productId, $title, $rightContent, $leftContent, $templateId, $id]);

        // Handle deleted images
        if (!empty($_POST['deleted_images'])) {
            $deletedImages = explode(',', $_POST['deleted_images']);
            foreach ($deletedImages as $imageUrl) {
                if (!empty(trim($imageUrl))) {
                    // Delete from database
                    $stmt = $conn->prepare("DELETE FROM landing_page_images WHERE landing_page_id = ? AND image_url = ?");
                    $stmt->execute([$id, trim($imageUrl)]);

                    // Delete physical file
                    $filePath = '../../' . ltrim(trim($imageUrl), '/');
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
            }
        }

        // Handle new image uploads
        if (!empty($_FILES['landingPageImages'])) {
            $uploadDir = '../../uploads/products/landing/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Get current max order
            $stmt = $conn->prepare("SELECT COALESCE(MAX(ordre), -1) + 1 as next_order FROM landing_page_images WHERE landing_page_id = ?");
            $stmt->execute([$id]);
            $imageOrder = $stmt->fetchColumn();

            foreach ($_FILES['landingPageImages']['tmp_name'] as $key => $tmpName) {
                if (!empty($tmpName)) {
                    $fileName = uniqid() . '_' . $_FILES['landingPageImages']['name'][$key];
                    $filePath = $uploadDir . $fileName;

                    if (move_uploaded_file($tmpName, $filePath)) {
                        $stmt = $conn->prepare(
                            "INSERT INTO landing_page_images (landing_page_id, image_url, ordre) VALUES (?, ?, ?)"
                        );
                        $stmt->execute([$id, '/uploads/products/landing/' . $fileName, $imageOrder]);
                        $imageOrder++;
                    }
                }
            }
        }

        // Handle new URL images
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'imageUrls') === 0 && !empty($value)) {
                // Get current max order
                $stmt = $conn->prepare("SELECT COALESCE(MAX(ordre), -1) + 1 as next_order FROM landing_page_images WHERE landing_page_id = ?");
                $stmt->execute([$id]);
                $imageOrder = $stmt->fetchColumn();

                $stmt = $conn->prepare(
                    "INSERT INTO landing_page_images (landing_page_id, image_url, ordre) VALUES (?, ?, ?)"
                );
                $stmt->execute([$id, $value, $imageOrder]);
            }
        }

        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Landing page updated successfully']);
    } catch (PDOException $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Database error in handleUpdate: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred',
            'error_code' => $e->getCode()
        ]);
    } catch (Exception $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Error in handleUpdate: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error occurred',
            'error_code' => 500
        ]);
    }
}

function handleClone()
{
    global $conn;

    if (!$conn) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database connection error']);
        return;
    }

    if (!$conn->inTransaction()) {
        $conn->beginTransaction();
    }

    try {
        $id = $_GET['id'] ?? null;

        if (!$id) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Landing page ID is required']);
            return;
        }

        $conn->beginTransaction();

        // Get original landing page
        $stmt = $conn->prepare("SELECT * FROM landing_pages WHERE id = ?");
        $stmt->execute([$id]);
        $originalPage = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$originalPage) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Landing page not found']);
            return;
        }

        // Create new landing page with modified title
        $newTitle = $originalPage['titre'] . ' - نسخة';
        $newUrl = generateUniqueUrl($originalPage['produit_id']);

        $stmt = $conn->prepare(
            "INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url, template_id)
             VALUES (?, ?, ?, ?, ?, ?)"
        );
        $stmt->execute([
            $originalPage['produit_id'],
            $newTitle,
            $originalPage['contenu_droit'],
            $originalPage['contenu_gauche'],
            $newUrl,
            $originalPage['template_id'] ?? 'custom'
        ]);

        $newPageId = $conn->lastInsertId();

        // Clone images
        $stmt = $conn->prepare("SELECT * FROM landing_page_images WHERE landing_page_id = ? ORDER BY ordre");
        $stmt->execute([$id]);
        $images = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($images as $image) {
            $stmt = $conn->prepare(
                "INSERT INTO landing_page_images (landing_page_id, image_url, ordre) VALUES (?, ?, ?)"
            );
            $stmt->execute([$newPageId, $image['image_url'], $image['ordre']]);
        }

        // Update the lien_url with the actual landing page ID
        $finalUrl = '/landing-page-template.php?id=' . $newPageId;
        $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
        $stmt->execute([$finalUrl, $newPageId]);

        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Landing page cloned successfully', 'new_id' => $newPageId]);
    } catch (PDOException $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Database error in handleClone: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred',
            'error_code' => $e->getCode()
        ]);
    } catch (Exception $e) {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log('Error in handleClone: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error occurred',
            'error_code' => 500
        ]);
    }
}

// Route requests
$action = $_GET['action'] ?? '';

switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
        if ($action === 'clone') {
            handleClone();
        } else {
            handlePost();
        }
        break;
    case 'PUT':
        handleUpdate();
        break;
    case 'DELETE':
        handleDelete();
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
