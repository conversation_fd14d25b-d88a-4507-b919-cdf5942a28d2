<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>لوحة التحكم - متجر الكتب</title>
    <link rel="stylesheet" href="../css/style.css" />
    <link rel="stylesheet" href="css/admin.css" />
    <link rel="stylesheet" href="css/product-landing.css" />

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- TinyMCE will be loaded later -->
  </head>
  <body>
    <div class="notifications-container" id="notificationsContainer"></div>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
      <i class="fas fa-bars"></i>
    </button>

    <div class="admin-container">
      <!-- Sidebar -->
      <aside class="sidebar" id="sidebar">
        <div class="logo">
          <h1>لوحة التحكم</h1>
        </div>
        <nav class="admin-nav">
          <ul>
            <li class="active" data-section="dashboard">
              <i class="fas fa-home"></i>
              <span>الرئيسية</span>
            </li>
            <li data-section="books">
              <i class="fas fa-box"></i>
              <span>إدارة المنتجات</span>
            </li>
            <li data-section="orders">
              <i class="fas fa-shopping-cart"></i>
              <span>الطلبات</span>
            </li>
            <li data-section="landingPages">
              <i class="fas fa-bullhorn"></i>
              <span>صفحات هبوط</span>
            </li>
            <li data-section="settings">
              <i class="fas fa-cog"></i>
              <span>الإعدادات</span>
            </li>
            <li id="logoutBtn">
              <i class="fas fa-sign-out-alt"></i>
              <span>تسجيل الخروج</span>
            </li>
          </ul>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="main-content">
        <!-- Header with refresh button -->
        <div class="content-header">
          <h1 id="pageTitle">لوحة المعلومات</h1>
          <button
            id="refreshPageBtn"
            class="refresh-button"
            title="تحديث الصفحة"
          >
            <i class="fas fa-sync-alt"></i>
            <span>تحديث الصفحة</span>
          </button>
        </div>

        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
          <h2>لوحة المعلومات</h2>
          <div class="stats-grid">
            <div class="stat-card">
              <i class="fas fa-box"></i>
              <div class="stat-info">
                <h3>إجمالي المنتجات</h3>
                <p id="totalBooks">0</p>
              </div>
            </div>
            <div class="stat-card">
              <i class="fas fa-shopping-cart"></i>
              <div class="stat-info">
                <h3>الطلبات الجديدة</h3>
                <p id="newOrders">0</p>
              </div>
            </div>
            <div class="stat-card">
              <i class="fas fa-money-bill-wave"></i>
              <div class="stat-info">
                <h3>إجمالي المبيعات</h3>
                <p id="totalSales">0 دج</p>
              </div>
            </div>
          </div>

          <div class="recent-orders">
            <h3>آخر الطلبات</h3>
            <div class="table-responsive">
              <table id="recentOrdersTable">
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Products Management Section -->
        <section id="books" class="content-section">
          <h2>إدارة المنتجات</h2>
          <button id="addBookBtn" class="action-button">
            <i class="fas fa-plus"></i> إضافة منتج جديد
          </button>

          <div class="table-responsive">
            <table id="booksTable">
              <thead>
                <tr>
                  <th>الصورة</th>
                  <th>العنوان</th>
                  <th>النوع</th>
                  <th>السعر</th>
                  <th>المخزون</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </section>

        <!-- Orders Management Section -->
        <section id="orders" class="content-section">
          <h2>إدارة الطلبات</h2>
          <div class="orders-filters">
            <select id="orderStatusFilter">
              <option value="all">جميع الطلبات</option>
              <option value="en_attente">قيد الانتظار</option>
              <option value="payé">تم الدفع</option>
              <option value="expédié">تم الشحن</option>
            </select>
          </div>

          <div class="table-responsive">
            <table id="ordersTable">
              <thead>
                <tr>
                  <th>رقم الطلب</th>
                  <th>العميل</th>
                  <th>التفاصيل</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>التاريخ</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </section>

        <!-- Landing Pages Section -->
        <section id="landingPages" class="content-section">
          <h2>صفحات هبوط</h2>
          <button id="addLandingPageBtn" class="action-button">
            <i class="fas fa-plus"></i> أَضف صفحة هبوط
          </button>
          <button
            id="testLandingPageBtn"
            class="action-button"
            style="background: #f59e0b; margin-left: 10px"
            onclick="testLandingPageModal()"
          >
            <i class="fas fa-bug"></i> اختبار المودال
          </button>
          <!-- Landing Pages Container -->
          <div id="landingPagesContainer" style="margin-top: 20px">
            <!-- Landing pages will be loaded here dynamically -->
          </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
          <h2>الإعدادات</h2>
          <div class="settings-card">
            <h3>تغيير كلمة المرور</h3>
            <form id="changePasswordForm">
              <div class="form-group">
                <label>كلمة المرور الحالية</label>
                <input type="password" id="currentPassword" required />
              </div>
              <div class="form-group">
                <label>كلمة المرور الجديدة</label>
                <input type="password" id="newPassword" required />
              </div>
              <div class="form-group">
                <label>تأكيد كلمة المرور الجديدة</label>
                <input type="password" id="confirmPassword" required />
              </div>
              <button type="submit" class="action-button">
                تحديث كلمة المرور
              </button>
            </form>
          </div>

          <!-- Landing Page Modal -->
          <div id="landingPageModal" class="modal">
            <div class="modal-content product-modal">
              <div class="modal-header">
                <h3>إضافة صفحة هبوط جديدة</h3>
                <button
                  type="button"
                  class="close-modal-btn"
                  onclick="landingPagesManager.closeModal()"
                  style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    float: left;
                  "
                >
                  ×
                </button>
              </div>

              <!-- Template Selection Step -->
              <div id="templateSelectionStep" class="modal-step active">
                <h4 style="margin-bottom: 20px; color: #667eea">
                  🎨 اختر قالب الصفحة
                </h4>
                <div
                  class="template-grid"
                  id="templateGrid"
                  style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 15px;
                    margin-bottom: 20px;
                  "
                >
                  <!-- Templates will be loaded here -->
                </div>
                <div class="step-actions" style="text-align: center">
                  <button
                    type="button"
                    class="action-button"
                    onclick="landingPagesManager.nextStep()"
                    disabled
                    id="nextStepBtn"
                  >
                    التالي - إعداد المحتوى
                  </button>
                </div>
              </div>

              <!-- Content Step -->
              <div id="contentStep" class="modal-step" style="display: none">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                  "
                >
                  <h4 style="color: #667eea">📝 إعداد المحتوى والتخطيط</h4>
                  <button
                    type="button"
                    class="cancel-button"
                    onclick="landingPagesManager.previousStep()"
                  >
                    العودة للقوالب
                  </button>
                </div>

                <!-- Layout Controls -->
                <div
                  class="layout-controls"
                  style="
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                  "
                >
                  <h5 style="margin: 0 0 15px 0; color: #495057">
                    ⚙️ إعدادات التخطيط
                  </h5>
                  <div
                    style="
                      display: grid;
                      grid-template-columns: 1fr 1fr;
                      gap: 15px;
                    "
                  >
                    <div class="form-group">
                      <label>موضع الصور:</label>
                      <select
                        id="imagePosition"
                        name="image_position"
                        style="
                          width: 100%;
                          padding: 8px;
                          border: 1px solid #ddd;
                          border-radius: 4px;
                        "
                      >
                        <option value="left">يسار</option>
                        <option value="center" selected>وسط</option>
                        <option value="right">يمين</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label>ترتيب النصوص:</label>
                      <select
                        id="textPosition"
                        name="text_position"
                        style="
                          width: 100%;
                          padding: 8px;
                          border: 1px solid #ddd;
                          border-radius: 4px;
                        "
                      >
                        <option value="left">يسار</option>
                        <option value="center">وسط</option>
                        <option value="right">يمين</option>
                        <option value="split" selected>
                          مقسم (يمين ويسار)
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <form id="landingPageForm" class="product-form">
                  <input type="hidden" id="selectedTemplate" name="template" />
                  <input type="hidden" id="landingPageId" name="id" />
                  <div class="form-group">
                    <label>اختر المنتج</label>
                    <select
                      id="productSelect"
                      name="productSelect"
                      required
                    ></select>
                    <div
                      id="productSelectionStatus"
                      class="product-selection-status"
                    ></div>
                  </div>
                  <div class="form-group">
                    <label>عنوان الصفحة</label>
                    <input
                      type="text"
                      id="landingPageTitle"
                      name="landingPageTitle"
                      required
                      placeholder="مثال: فن اللامبالاة - نهج معاكس للعيش حياة جيدة"
                    />
                  </div>
                  <div class="form-group">
                    <label>🖼️ صور العرض المتحركة (Carousel)</label>
                    <div
                      style="
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        margin-bottom: 15px;
                      "
                    >
                      <h4 style="margin: 0 0 10px 0; color: #667eea">
                        📁 رفع الصور من الجهاز
                      </h4>
                      <input
                        type="file"
                        id="landingPageImages"
                        multiple
                        accept="image/*"
                        style="margin-bottom: 10px"
                      />
                      <small style="color: #666; display: block">
                        💡 اختر عدة صور لعرضها في شكل carousel متحرك في أعلى
                        الصفحة
                      </small>
                      <div id="imagePreview" class="image-preview"></div>
                    </div>

                    <div
                      style="
                        background: #f0f8ff;
                        padding: 15px;
                        border-radius: 8px;
                      "
                    >
                      <h4 style="margin: 0 0 10px 0; color: #667eea">
                        🌐 إضافة صور من الإنترنت
                      </h4>
                      <div id="urlImagesContainer">
                        <div
                          class="url-image-input"
                          style="
                            display: flex;
                            align-items: center;
                            margin-bottom: 10px;
                          "
                        >
                          <input
                            type="url"
                            name="imageUrls[]"
                            placeholder="https://images.unsplash.com/photo-example.jpg"
                            style="
                              flex: 1;
                              padding: 8px;
                              border: 1px solid #ddd;
                              border-radius: 4px;
                              margin-left: 10px;
                            "
                          />
                          <button
                            type="button"
                            onclick="addUrlImageInput()"
                            class="add-btn"
                            style="
                              background: #28a745;
                              color: white;
                              border: none;
                              padding: 8px 12px;
                              border-radius: 4px;
                              cursor: pointer;
                              margin-left: 5px;
                            "
                            title="إضافة صورة أخرى"
                          >
                            ➕
                          </button>
                        </div>
                      </div>
                      <small style="color: #666">
                        💡 يمكنك إضافة صور من مواقع مثل Unsplash, Pixabay, أو أي
                        رابط صورة صالح
                      </small>
                    </div>
                  </div>
                  <div
                    style="
                      display: grid;
                      grid-template-columns: 1fr 1fr;
                      gap: 20px;
                      margin-top: 20px;
                    "
                  >
                    <div class="form-group">
                      <label
                        style="
                          background: #e8f4fd;
                          padding: 10px;
                          border-radius: 5px;
                          display: block;
                          margin-bottom: 10px;
                        "
                      >
                        📝 المحتوى الأيمن (مميزات المنتج)
                      </label>
                      <small
                        style="color: #666; display: block; margin-bottom: 10px"
                      >
                        💡 أضف هنا: المميزات، الفوائد، المواصفات التقنية، ما
                        يميز المنتج
                      </small>
                      <textarea
                        id="rightContent"
                        name="rightContent"
                        class="tinymce"
                        placeholder="مثال:
🎯 لماذا هذا المنتج مميز؟
• مميزة رقم 1
• مميزة رقم 2
• مميزة رقم 3

💡 ما ستحصل عليه:
• فائدة 1
• فائدة 2"
                      ></textarea>
                    </div>

                    <div class="form-group">
                      <label
                        style="
                          background: #fff2e8;
                          padding: 10px;
                          border-radius: 5px;
                          display: block;
                          margin-bottom: 10px;
                        "
                      >
                        📄 المحتوى الأيسر (تفاصيل إضافية)
                      </label>
                      <small
                        style="color: #666; display: block; margin-bottom: 10px"
                      >
                        💡 أضف هنا: معلومات عن الشركة، آراء العملاء، ضمانات،
                        عروض خاصة
                      </small>
                      <textarea
                        id="leftContent"
                        name="leftContent"
                        class="tinymce"
                        placeholder="مثال:
🏢 عن الشركة
معلومات عن الشركة المصنعة...

🌟 آراء العملاء
'منتج ممتاز، أنصح به بشدة' - أحمد محمد

🎁 عرض خاص
خصم 20% لفترة محدودة!"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-actions">
                    <button type="submit" class="action-button">حفظ</button>
                    <button
                      type="button"
                      class="cancel-button"
                      onclick="landingPagesManager.closeModal()"
                    >
                      إلغاء
                    </button>
                  </div>
                </form>
              </div>
              <!-- End Content Step -->
            </div>
          </div>

          <div class="settings-card">
            <h3>إعدادات المتجر</h3>
            <form id="storeSettingsForm">
              <div class="form-group">
                <label>اسم المتجر</label>
                <input type="text" id="storeName" required />
              </div>
              <div class="form-group">
                <label>رقم الهاتف</label>
                <input type="tel" id="storePhone" required />
              </div>
              <div class="form-group">
                <label>البريد الإلكتروني</label>
                <input type="email" id="storeEmail" required />
              </div>
              <div class="form-group">
                <label>العنوان</label>
                <textarea id="storeAddress" class="tinymce" required></textarea>
              </div>
              <button type="submit" class="action-button">حفظ الإعدادات</button>
            </form>
          </div>
        </section>
      </main>
    </div>

    <!-- Add/Edit Product Modal -->
    <div id="bookModal" class="modal">
      <div class="modal-content product-modal">
        <span class="close">&times;</span>
        <h3 id="modalTitle">إضافة منتج جديد</h3>
        <form id="bookForm" class="product-form">
          <div class="form-group">
            <label>نوع المنتج</label>
            <select id="productType" class="product-type-select" required>
              <option value="book">كتاب</option>
              <option value="backpack">حقيبة ظهر</option>
              <option value="laptop">حاسوب محمول</option>
            </select>
          </div>

          <!-- Common Fields -->
          <div class="form-group">
            <label>عنوان المنتج</label>
            <input type="text" id="productTitle" required />
          </div>
          <div class="form-group">
            <label>الوصف</label>
            <textarea
              id="productDescription"
              class="tinymce"
              required
            ></textarea>
          </div>
          <div class="form-group">
            <label>السعر (دج)</label>
            <input
              type="number"
              id="productPrice"
              min="0"
              step="0.01"
              required
            />
          </div>
          <div class="form-group">
            <label>المخزون</label>
            <input type="number" id="productStock" min="0" required />
          </div>

          <!-- Book Specific Fields -->
          <div id="bookFields" class="field-group active">
            <div class="form-group">
              <label>المؤلف</label>
              <input type="text" id="bookAuthor" />
            </div>
          </div>

          <!-- Backpack Specific Fields -->
          <div id="backpackFields" class="field-group">
            <div class="form-group">
              <label>المواد</label>
              <input type="text" id="backpackMaterial" />
            </div>
            <div class="form-group">
              <label>السعة (لتر)</label>
              <input type="number" id="backpackCapacity" min="0" />
            </div>
          </div>

          <!-- Laptop Specific Fields -->
          <div id="laptopFields" class="field-group">
            <div class="form-group">
              <label>المعالج</label>
              <input type="text" id="laptopProcessor" />
            </div>
            <div class="form-group">
              <label>الذاكرة العشوائية (GB)</label>
              <input type="number" id="laptopRam" min="0" />
            </div>
            <div class="form-group">
              <label>التخزين (GB)</label>
              <input type="number" id="laptopStorage" min="0" />
            </div>
          </div>

          <div class="form-group">
            <label>صورة المنتج</label>
            <input type="file" id="productImage" accept="image/*" />
          </div>
          <button type="submit" class="action-button">حفظ</button>
        </form>
      </div>
    </div>

    <!-- Order Details Modal -->
    <div id="orderModal" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h3>تفاصيل الطلب</h3>
        <div id="orderDetails"></div>
      </div>
    </div>

    <!-- Scripts -->
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"
      integrity="sha512-6JR4bbn8rCKvrkdoTJd/VFyXAN4CE9XMtgykPWgKiHjou56YDJxWsi90hAeMTYxNwUnKSQu9JPc3SQUg+aGCHw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script src="js/tinymce-config.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        initTinyMCE();
      });
    </script>
    <script src="js/admin.js"></script>
    <script src="js/landing-pages.js"></script>
    <script src="js/product-landing.js"></script>
  </body>
</html>
